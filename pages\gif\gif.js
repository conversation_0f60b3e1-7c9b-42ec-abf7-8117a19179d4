//gif.js
Page({
  data: {
    gifUrl: '', // GIF图片路径
    saving: false,
    adUnitId: 'your-ad-unit-id', // 需要替换为你的广告位ID
    adIntervals: 30,
    openid: '',
    isExpired: false,
    loading: true
  },

  onLoad(options) {
    // 获取传递的openid参数
    if (options.openid) {
      this.setData({
        openid: options.openid
      })
      this.getImageByOpenid(options.openid)
    } else {
      // 如果没有openid，显示错误状态
      this.setData({
        isExpired: true,
        loading: false
      })
    }
  },

  // 根据openid获取图片
  getImageByOpenid(openid) {
    wx.request({
      url: 'https://photoconvert.cloud/api/getImage', // 替换为你的后端API地址
      method: 'POST',
      data: {
        openid: openid
      },
      success: (res) => {
        this.setData({
          loading: false
        })

        if (res.data.code === 0) {
          // 图片未过期，显示图片
          this.setData({
            gifUrl: res.data.imageUrl,
            isExpired: false
          })
        } else if (res.data.code === 1) {
          // 图片过期
          this.setData({
            isExpired: true
          })
        }
      },
      fail: (err) => {
        this.setData({
          loading: false,
          isExpired: true
        })
        console.error('获取图片失败:', err)
      }
    })
  },

  // 保存图片到相册
  saveImage() {
    if (this.data.saving) return
    
    this.setData({ saving: true })
    
    // 先检查是否有保存到相册的权限
    wx.getSetting({
      success: (res) => {
        if (res.authSetting['scope.writePhotosAlbum']) {
          this.downloadAndSave()
        } else {
          // 请求授权
          wx.authorize({
            scope: 'scope.writePhotosAlbum',
            success: () => {
              this.downloadAndSave()
            },
            fail: () => {
              this.setData({ saving: false })
              wx.showModal({
                title: '提示',
                content: '需要您授权保存图片到相册',
                showCancel: false
              })
            }
          })
        }
      }
    })
  },

  // 下载并保存图片
  downloadAndSave() {
    wx.downloadFile({
      url: this.data.gifUrl,
      success: (res) => {
        if (res.statusCode === 200) {
          wx.saveImageToPhotosAlbum({
            filePath: res.tempFilePath,
            success: () => {
              this.setData({ saving: false })
              wx.showToast({
                title: '保存成功',
                icon: 'success',
                duration: 2000
              })
            },
            fail: (err) => {
              this.setData({ saving: false })
              wx.showToast({
                title: '保存失败',
                icon: 'none',
                duration: 2000
              })
            }
          })
        } else {
          this.setData({ saving: false })
          wx.showToast({
            title: '下载失败',
            icon: 'none',
            duration: 2000
          })
        }
      },
      fail: () => {
        this.setData({ saving: false })
        wx.showToast({
          title: '下载失败',
          icon: 'none',
          duration: 2000
        })
      }
    })
  },

  // 广告加载成功
  onAdLoad() {
    console.log('广告加载成功')
  },

  // 广告加载失败
  onAdError(err) {
    console.log('广告加载失败', err)
  }
})
