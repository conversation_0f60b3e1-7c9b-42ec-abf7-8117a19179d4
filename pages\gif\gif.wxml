<!--gif.wxml-->
<view class="container">
  <!-- 顶部导航栏 -->
  <view class="nav-bar">
    <view class="nav-back" bindtap="goBack">
      <text class="nav-back-icon">←</text>
      <text class="nav-back-text">返回</text>
    </view>
    <view class="nav-title">表情包</view>
    <view class="nav-placeholder"></view>
  </view>
  <!-- 加载状态 -->
  <view class="gif-container" wx:if="{{loading}}">
    <view class="loading-container">
      <text class="loading-text">加载中...</text>
    </view>
  </view>

  <!-- 图片过期状态 -->
  <view class="gif-container" wx:elif="{{isExpired}}">
    <view class="expired-container">
      <text class="expired-emoji">😞</text>
      <text class="expired-text">图片过期，请重新发送！</text>
    </view>
  </view>

  <!-- 正常显示GIF -->
  <view class="gif-container" wx:else>
    <image class="gif-image" src="{{gifUrl}}" mode="aspectFit"></image>
  </view>

  <!-- 保存按钮 -->
  <view class="save-container" wx:if="{{!loading && !isExpired}}">
    <button class="save-btn" bindtap="saveImage" loading="{{saving}}">
      {{saving ? '保存中...' : '保存到手机'}}
    </button>
  </view>

  <!-- 广告位 -->
  <view class="ad-container">
    <ad unit-id="{{adUnitId}}" ad-intervals="{{adIntervals}}" binderror="onAdError" bindload="onAdLoad"></ad>
  </view>
</view>
