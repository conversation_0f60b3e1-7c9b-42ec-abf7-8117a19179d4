# 表情包保存工具 - 微信小程序

这是一个微信小程序，用于浏览和保存GIF表情包到手机相册。

## 功能特点

1. **表情包首页** - 1行3列网格布局，展示20个精选表情包预览
2. **表情包详情** - 点击表情图标可查看完整的GIF动画
3. **GIF保存功能** - 可以保存GIF图片到手机相册
4. **广告位集成** - 支持微信小程序广告
5. **保存成功提示** - 保存后显示Toast提示
6. **优雅的UI设计** - 现代化的界面设计和流畅的交互体验

## 页面结构

- `pages/index/` - 首页（表情包网格列表）
- `pages/gif/` - GIF详情和保存页面

## 新功能说明

### 首页表情包网格
- 采用1行3列的响应式网格布局
- 总共展示20个精选表情包
- 每个表情包显示emoji图标和名称
- 点击任意表情包可跳转到详情页面
- 支持滚动浏览所有表情包

### 表情包详情页
- 支持通过URL参数传递图片链接
- 兼容原有的openid方式
- 新增返回按钮，方便用户导航
- 优化的UI设计，更好的视觉体验

## 配置说明

### 1. 公众号跳转配置
在 `pages/index/index.js` 中需要配置：
- `appId`: 你的公众号对应的小程序AppId
- 公众号名称：用于复制到剪贴板

### 2. 广告配置
在 `pages/gif/gif.js` 中需要配置：
- `adUnitId`: 你的广告位ID

### 3. 图片资源
需要在 `images/` 目录下添加：
- `download-icon.png` - 下载图标
- `sample-gif.gif` - 示例GIF图片

## 使用方法

1. 用户打开小程序，默认显示引导页
2. 点击"打开公众号"按钮跳转到公众号
3. 在GIF页面可以查看和保存表情包
4. 点击"保存到手机"按钮保存图片到相册

## 权限说明

小程序需要以下权限：
- `scope.writePhotosAlbum` - 保存图片到相册

## 开发环境

- 微信开发者工具
- 小程序基础库版本 2.0+

## 部署步骤

1. **准备图片资源**
   - 在 `images/` 目录下添加 `download-icon.png` 和 `sample-gif.gif`
   - 参考 `images/README.md` 中的说明

2. **配置小程序信息**
   - 在 `project.config.json` 中修改 `appid` 为你的小程序AppId
   - 在微信开发者工具中导入项目

3. **配置公众号跳转**
   - 修改 `pages/index/index.js` 中的公众号相关配置

4. **配置广告**
   - 在微信公众平台开通广告功能
   - 修改 `pages/gif/gif.js` 中的广告位ID

5. **测试功能**
   - 在首页连续点击标题5次可以进入GIF页面进行测试

## 注意事项

1. 需要在微信公众平台配置小程序信息
2. 广告功能需要开通微信小程序广告
3. 图片保存功能需要用户授权
4. GIF图片需要是网络地址或本地临时文件路径
5. 测试时可以使用隐藏入口（连续点击首页标题5次）进入GIF页面
