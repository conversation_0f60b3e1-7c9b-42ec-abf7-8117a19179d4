//index.js
Page({
  data: {
    tapCount: 0,
    emojiList: [
      { id: 1, emoji: '😂', name: '笑哭', gifUrl: 'https://mmbiz.qpic.cn/sz_mmbiz_gif/r7EiccuXljOiaqa0AcZwulJyTnt8yI7DicibVsvChz513icgrBJOib8Fv23oz24Mp0GdUakNibUsqdSGKBHdwAoQ833og/640?wx_fmt=gif/mmbizgif' },
      { id: 2, emoji: '😍', name: '爱心眼', gifUrl: 'https://mmbiz.qpic.cn/sz_mmbiz_gif/r7EiccuXljOiaqa0AcZwulJyTnt8yI7Dicib3coz08VHe6YeHnokgZfxkJUiarHFEQsmxiaUbJs0DZGicj3cPxQSm94hg/640?wx_fmt=gif/mmbizgif' },
      { id: 3, emoji: '🤔', name: '思考', gifUrl: 'https://mmbiz.qpic.cn/sz_mmbiz_gif/r7EiccuXljOiaqa0AcZwulJyTnt8yI7DicibHg8ib3WBx897CeQnEXIa5HFa1AzVlk19NXKCBEIRSGrokQtQc5xkQTw/640?wx_fmt=gif/mmbizgif' },
      { id: 4, emoji: '😭', name: '大哭', gifUrl: 'https://mmbiz.qpic.cn/sz_mmbiz_gif/r7EiccuXljOiaqa0AcZwulJyTnt8yI7DicibS4Lkic8VglN6C8rBRlMKwGyNiaViaicG8jQktGQPlUkiaulHGauuI8HoMrw/640?wx_fmt=gif/mmbizgif' },
      { id: 5, emoji: '😎', name: '酷', gifUrl: 'https://mmbiz.qpic.cn/sz_mmbiz_gif/r7EiccuXljOiaqa0AcZwulJyTnt8yI7DicibUZ0M5zImyqJdk4GrIeZAatq6C4FS6M5KmEaF08ZmiaUvQyuqN2XA25A/640?wx_fmt=gif/mmbizgif' },
      { id: 6, emoji: '🤣', name: '笑翻', gifUrl: 'https://mmbiz.qpic.cn/sz_mmbiz_gif/r7EiccuXljOiaqa0AcZwulJyTnt8yI7Dicib6g7Zsica2bFkFpSBD45S7ZnAJC7FFPMbxmDAibQ2pmVsMhliaLRLXSEKw/640?wx_fmt=gif/mmbizgif' },
      { id: 7, emoji: '😘', name: '飞吻', gifUrl: 'https://mmbiz.qpic.cn/sz_mmbiz_gif/r7EiccuXljOiaqa0AcZwulJyTnt8yI7DicibOxia41lGCosmWKeGtickITrBicrPQy6ErNRD9J35jyvZGUOicY8Z8fMYyg/640?wx_fmt=gif/mmbizgif' },
      { id: 8, emoji: '🙄', name: '翻白眼', gifUrl: 'https://mmbiz.qpic.cn/sz_mmbiz_jpg/r7EiccuXljOiaqa0AcZwulJyTnt8yI7DicibEGpLy32ibw9rmbMiaWxGrQwl0qCSibnmJSvicx9sKBGMqjUd67jGvyexkQ/640?wx_fmt=jpeg' },
      { id: 9, emoji: '😴', name: '睡觉', gifUrl: 'https://mmbiz.qpic.cn/sz_mmbiz_jpg/r7EiccuXljOiaqa0AcZwulJyTnt8yI7DicibsiaS0RU1HCYEsdtmSNmhZOLXZhyQrSZfnLtQ7e8icicKPt9NnPUxRopUw/640?wx_fmt=jpeg' },
      { id: 10, emoji: '🤯', name: '爆炸', gifUrl: 'https://mmbiz.qpic.cn/sz_mmbiz_png/r7EiccuXljOiaqa0AcZwulJyTnt8yI7DicibaPnz2YlchVNoRcnzj5O53rgQGoRbTRtj1o5eurtYLdH8IB39wDb1lg/640?wx_fmt=png' },
      { id: 11, emoji: '😤', name: '生气', gifUrl: 'https://mmbiz.qpic.cn/sz_mmbiz_gif/r7EiccuXljOiaqa0AcZwulJyTnt8yI7DicibianQR18F0nG2O5wmD7ySZW23uunk4k1qeRaL70EQibrurveP1B9qObRA/640?wx_fmt=gif/mmbizgif' },
      { id: 12, emoji: '🥺', name: '可怜', gifUrl: 'https://mmbiz.qpic.cn/sz_mmbiz_gif/r7EiccuXljOh7TMYoZWXemafo8tZJ4WA8bLgbtpGr7icmeSwpibNYDgib6nZiaF4MtPiaUYzG78f43AbDXyicB3bZQficQ/640?wx_fmt=gif/mmbizgif' },
      { id: 13, emoji: '😏', name: '坏笑', gifUrl: 'https://mmbiz.qpic.cn/sz_mmbiz_gif/r7EiccuXljOiaqa0AcZwulJyTnt8yI7DicibVsvChz513icgrBJOib8Fv23oz24Mp0GdUakNibUsqdSGKBHdwAoQ833og/640?wx_fmt=gif/mmbizgif' },
      { id: 14, emoji: '🤪', name: '疯狂', gifUrl: 'https://mmbiz.qpic.cn/sz_mmbiz_gif/r7EiccuXljOiaqa0AcZwulJyTnt8yI7Dicib3coz08VHe6YeHnokgZfxkJUiarHFEQsmxiaUbJs0DZGicj3cPxQSm94hg/640?wx_fmt=gif/mmbizgif' },
      { id: 15, emoji: '😱', name: '惊恐', gifUrl: 'https://mmbiz.qpic.cn/sz_mmbiz_gif/r7EiccuXljOiaqa0AcZwulJyTnt8yI7DicibHg8ib3WBx897CeQnEXIa5HFa1AzVlk19NXKCBEIRSGrokQtQc5xkQTw/640?wx_fmt=gif/mmbizgif' },
      { id: 16, emoji: '🤗', name: '拥抱', gifUrl: 'https://mmbiz.qpic.cn/sz_mmbiz_gif/r7EiccuXljOiaqa0AcZwulJyTnt8yI7DicibS4Lkic8VglN6C8rBRlMKwGyNiaViaicG8jQktGQPlUkiaulHGauuI8HoMrw/640?wx_fmt=gif/mmbizgif' },
      { id: 17, emoji: '😋', name: '美味', gifUrl: 'https://mmbiz.qpic.cn/sz_mmbiz_gif/r7EiccuXljOiaqa0AcZwulJyTnt8yI7DicibUZ0M5zImyqJdk4GrIeZAatq6C4FS6M5KmEaF08ZmiaUvQyuqN2XA25A/640?wx_fmt=gif/mmbizgif' },
      { id: 18, emoji: '🤭', name: '偷笑', gifUrl: 'https://mmbiz.qpic.cn/sz_mmbiz_gif/r7EiccuXljOiaqa0AcZwulJyTnt8yI7Dicib6g7Zsica2bFkFpSBD45S7ZnAJC7FFPMbxmDAibQ2pmVsMhliaLRLXSEKw/640?wx_fmt=gif/mmbizgif' },
      { id: 19, emoji: '😵', name: '晕', gifUrl: 'https://mmbiz.qpic.cn/sz_mmbiz_gif/r7EiccuXljOiaqa0AcZwulJyTnt8yI7DicibOxia41lGCosmWKeGtickITrBicrPQy6ErNRD9J35jyvZGUOicY8Z8fMYyg/640?wx_fmt=gif/mmbizgif' },
      { id: 20, emoji: '🥳', name: '庆祝', gifUrl: 'https://mmbiz.qpic.cn/sz_mmbiz_gif/r7EiccuXljOiaqa0AcZwulJyTnt8yI7DicibianQR18F0nG2O5wmD7ySZW23uunk4k1qeRaL70EQibrurveP1B9qObRA/640?wx_fmt=gif/mmbizgif' }
    ]
  },

  onLoad() {

  },

  // 点击表情图标跳转到gif页面
  onEmojiTap(e) {
    const { id, gifUrl } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/gif/gif?imageUrl=${encodeURIComponent(gifUrl)}&id=${id}`
    })
  },

  // 打开公众号
  openWechatAccount() {
    wx.navigateToMiniProgram({
      appId: 'wx7927fc540069a9ba', // 这里需要填入你的公众号对应的小程序appId
      path: '', // 可选，打开的页面路径
      extraData: {
        from: 'miniprogram'
      },
      envVersion: 'release',
      success(res) {
        console.log('跳转成功')
      },
      fail(res) {
        // 如果跳转失败，可以复制公众号名称到剪贴板
        wx.setClipboardData({
          data: '你的公众号名称', // 这里填入你的公众号名称
          success: function(res) {
            wx.showToast({
              title: '公众号名称已复制',
              icon: 'success',
              duration: 2000
            })
          }
        })
      }
    })
  },

  // 隐藏入口：连续点击标题5次可以进入GIF页面（用于测试）
  onTitleTap() {
    this.setData({
      tapCount: this.data.tapCount + 1
    })

    if (this.data.tapCount >= 5) {
      // 测试用的openid，实际使用时应该从登录获取
      const testOpenid = 'oft-jviMe0JPszvjOVASgnja8U0o'
      wx.navigateTo({
        url: `/pages/gif/gif?openid=${testOpenid}`
      })
      this.setData({
        tapCount: 0
      })
    }

    // 3秒后重置计数
    setTimeout(() => {
      this.setData({
        tapCount: 0
      })
    }, 3000)
  }
})
