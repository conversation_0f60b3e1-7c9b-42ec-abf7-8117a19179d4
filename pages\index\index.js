//index.js
Page({
  data: {
    tapCount: 0
  },

  onLoad() {

  },

  // 打开公众号
  openWechatAccount() {
    wx.navigateToMiniProgram({
      appId: 'wx7927fc540069a9ba', // 这里需要填入你的公众号对应的小程序appId
      path: '', // 可选，打开的页面路径
      extraData: {
        from: 'miniprogram'
      },
      envVersion: 'release',
      success(res) {
        console.log('跳转成功')
      },
      fail(res) {
        // 如果跳转失败，可以复制公众号名称到剪贴板
        wx.setClipboardData({
          data: '你的公众号名称', // 这里填入你的公众号名称
          success: function(res) {
            wx.showToast({
              title: '公众号名称已复制',
              icon: 'success',
              duration: 2000
            })
          }
        })
      }
    })
  },

  // 隐藏入口：连续点击标题5次可以进入GIF页面（用于测试）
  onTitleTap() {
    this.setData({
      tapCount: this.data.tapCount + 1
    })

    if (this.data.tapCount >= 5) {
      // 测试用的openid，实际使用时应该从登录获取
      const testOpenid = 'oft-jviMe0JPszvjOVASgnja8U0o'
      wx.navigateTo({
        url: `/pages/gif/gif?openid=${testOpenid}`
      })
      this.setData({
        tapCount: 0
      })
    }

    // 3秒后重置计数
    setTimeout(() => {
      this.setData({
        tapCount: 0
      })
    }, 3000)
  }
})
