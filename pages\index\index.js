//index.js
Page({
  data: {
    tapCount: 0,
    emojiList: [
      { id: 1, emoji: '😂', name: '笑哭', gifUrl: 'https://media.giphy.com/media/3oEjI6SIIHBdRxXI40/giphy.gif' },
      { id: 2, emoji: '😍', name: '爱心眼', gifUrl: 'https://media.giphy.com/media/26ufdipQqU2lhNA4g/giphy.gif' },
      { id: 3, emoji: '🤔', name: '思考', gifUrl: 'https://media.giphy.com/media/3o7btPCcdNniyf0ArS/giphy.gif' },
      { id: 4, emoji: '😭', name: '大哭', gifUrl: 'https://media.giphy.com/media/OPU6wzx8JrHna/giphy.gif' },
      { id: 5, emoji: '😎', name: '酷', gifUrl: 'https://media.giphy.com/media/ZVik7pBtu9dNS/giphy.gif' },
      { id: 6, emoji: '🤣', name: '笑翻', gifUrl: 'https://media.giphy.com/media/l3q2K5jinAlChoCLS/giphy.gif' },
      { id: 7, emoji: '😘', name: '飞吻', gifUrl: 'https://media.giphy.com/media/M90mJvfWfd5mbUuULX/giphy.gif' },
      { id: 8, emoji: '🙄', name: '翻白眼', gifUrl: 'https://media.giphy.com/media/Rhhr8D5mKSX7O/giphy.gif' },
      { id: 9, emoji: '😴', name: '睡觉', gifUrl: 'https://media.giphy.com/media/VbnUQpnihPSIgIXuZv/giphy.gif' },
      { id: 10, emoji: '🤯', name: '爆炸', gifUrl: 'https://media.giphy.com/media/26ufdipQqU2lhNA4g/giphy.gif' },
      { id: 11, emoji: '😤', name: '生气', gifUrl: 'https://media.giphy.com/media/12XMGIWtrHBl5e/giphy.gif' },
      { id: 12, emoji: '🥺', name: '可怜', gifUrl: 'https://media.giphy.com/media/L95W4wv8nnb9K/giphy.gif' },
      { id: 13, emoji: '😏', name: '坏笑', gifUrl: 'https://media.giphy.com/media/kPtv3UIPrv36cjxqLs/giphy.gif' },
      { id: 14, emoji: '🤪', name: '疯狂', gifUrl: 'https://media.giphy.com/media/3o6Zt4HU9uwXmXSAuI/giphy.gif' },
      { id: 15, emoji: '😱', name: '惊恐', gifUrl: 'https://media.giphy.com/media/3o72FcJmLzIdYJdmDe/giphy.gif' },
      { id: 16, emoji: '🤗', name: '拥抱', gifUrl: 'https://media.giphy.com/media/3M4NpbLCTxBqU/giphy.gif' },
      { id: 17, emoji: '😋', name: '美味', gifUrl: 'https://media.giphy.com/media/Zk9mW5OmXTz9e/giphy.gif' },
      { id: 18, emoji: '🤭', name: '偷笑', gifUrl: 'https://media.giphy.com/media/9MFsKQ8A6HCN2/giphy.gif' },
      { id: 19, emoji: '😵', name: '晕', gifUrl: 'https://media.giphy.com/media/xUOxf3yDKCuwpOlT3i/giphy.gif' },
      { id: 20, emoji: '🥳', name: '庆祝', gifUrl: 'https://media.giphy.com/media/artj92V8o75VPL7AeQ/giphy.gif' }
    ]
  },

  onLoad() {

  },

  // 点击表情图标跳转到gif页面
  onEmojiTap(e) {
    const { id, gifUrl } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/gif/gif?imageUrl=${encodeURIComponent(gifUrl)}&id=${id}`
    })
  },

  // 打开公众号
  openWechatAccount() {
    wx.navigateToMiniProgram({
      appId: 'wx7927fc540069a9ba', // 这里需要填入你的公众号对应的小程序appId
      path: '', // 可选，打开的页面路径
      extraData: {
        from: 'miniprogram'
      },
      envVersion: 'release',
      success(res) {
        console.log('跳转成功')
      },
      fail(res) {
        // 如果跳转失败，可以复制公众号名称到剪贴板
        wx.setClipboardData({
          data: '你的公众号名称', // 这里填入你的公众号名称
          success: function(res) {
            wx.showToast({
              title: '公众号名称已复制',
              icon: 'success',
              duration: 2000
            })
          }
        })
      }
    })
  },

  // 隐藏入口：连续点击标题5次可以进入GIF页面（用于测试）
  onTitleTap() {
    this.setData({
      tapCount: this.data.tapCount + 1
    })

    if (this.data.tapCount >= 5) {
      // 测试用的openid，实际使用时应该从登录获取
      const testOpenid = 'oft-jviMe0JPszvjOVASgnja8U0o'
      wx.navigateTo({
        url: `/pages/gif/gif?openid=${testOpenid}`
      })
      this.setData({
        tapCount: 0
      })
    }

    // 3秒后重置计数
    setTimeout(() => {
      this.setData({
        tapCount: 0
      })
    }, 3000)
  }
})
