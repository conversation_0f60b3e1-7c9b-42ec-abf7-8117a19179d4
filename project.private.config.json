{"libVersion": "3.8.12", "projectname": "表情保存小助手2025", "condition": {"miniprogram": {"list": [{"name": "pages/gif/gif", "pathName": "pages/gif/gif", "query": "openid=oft-jviMe0JPszvjOVASgnja8U0o", "scene": null, "launchMode": "default"}]}}, "setting": {"urlCheck": false, "coverView": true, "lazyloadPlaceholderEnable": false, "skylineRenderEnable": false, "preloadBackgroundData": false, "autoAudits": false, "useApiHook": true, "useApiHostProcess": true, "showShadowRootInWxmlPanel": true, "useStaticServer": false, "useLanDebug": false, "showES6CompileOption": false, "compileHotReLoad": true, "checkInvalidKey": true, "ignoreDevUnusedFiles": true, "bigPackageSizeSupport": false, "useIsolateContext": true}}