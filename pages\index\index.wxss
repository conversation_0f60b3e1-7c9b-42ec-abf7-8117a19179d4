/**index.wxss**/
.container {
  height: 80vh;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.content {
  text-align: center;
  padding: 0 60rpx;
}

.title {
  font-size: 48rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 80rpx;
}

.icon-container {
  margin-bottom: 40rpx;
}

.download-icon {
  width: 140rpx;
  height: 140rpx;
  /* background-color: #4A90E2; */
  border-radius: 20rpx;
  /* padding: 20rpx; */
}

.subtitle {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 100rpx;
}

.wechat-btn {
  width: 90%;
  min-width: 550rpx;
  height: 88rpx;
  background-color: #4A90E2;
  color: white;
  border-radius: 10rpx;
  font-size: 32rpx;
  line-height: 60rpx;
  border: none;
  margin: 0;
}

.wechat-btn:active {
  background-color: #3A7BC8;
}
