/**index.wxss**/
.container {
  height: 100vh;
  background-color: #f8f9fa;
  display: flex;
  flex-direction: column;
}

.header {
  text-align: center;
  padding: 40rpx 60rpx 30rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: relative;
}

.header::after {
  content: '';
  position: absolute;
  bottom: -20rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background-color: rgba(255,255,255,0.3);
  border-radius: 3rpx;
}

.title {
  font-size: 44rpx;
  color: white;
  font-weight: 600;
  margin-bottom: 20rpx;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.2);
}

.subtitle {
  font-size: 28rpx;
  color: rgba(255,255,255,0.9);
}

.emoji-grid {
  flex: 1;
  padding: 30rpx 20rpx 20rpx;
}

.emoji-list {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24rpx;
  padding: 0;
}

.emoji-item {
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.08);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 160rpx;
}

.emoji-item:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.15);
  background-color: #f8f9fa;
}

.emoji-item:hover {
  box-shadow: 0 6rpx 20rpx rgba(0,0,0,0.12);
  transform: translateY(-2rpx);
}

.emoji-icon {
  font-size: 60rpx;
  margin-bottom: 15rpx;
  line-height: 1;
}

.emoji-name {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

.footer {
  padding: 20rpx 40rpx 40rpx;
  background-color: white;
  border-top: 1rpx solid #eee;
}

.wechat-btn {
  width: 100%;
  height: 80rpx;
  background-color: #4A90E2;
  color: white;
  border-radius: 40rpx;
  font-size: 28rpx;
  line-height: 80rpx;
  border: none;
  margin: 0;
}

.wechat-btn:active {
  background-color: #3A7BC8;
}
