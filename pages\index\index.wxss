/**index.wxss**/
.container {
  height: 100vh;
  background-color: #f8f9fa;
  display: flex;
  flex-direction: column;
}

.emoji-grid {
  flex: 1;
  padding: 20rpx;
}

.emoji-list {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24rpx;
  padding: 20rpx 0;
}

.emoji-item {
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.08);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 160rpx;
}

.emoji-item:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.15);
  background-color: #f8f9fa;
}

.emoji-item:hover {
  box-shadow: 0 6rpx 20rpx rgba(0,0,0,0.12);
  transform: translateY(-2rpx);
}

.emoji-icon {
  font-size: 60rpx;
  margin-bottom: 15rpx;
  line-height: 1;
}

.emoji-name {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

.hidden-trigger {
  height: 100rpx;
  width: 100%;
  /* 完全透明，用户看不到但可以点击 */
}


