/**gif.wxss**/
.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: white;
  padding-top: 20rpx;
}

.gif-container {
  padding: 20rpx 40rpx;
  background-color: white;
  margin: 0 20rpx;
  text-align: center;
  /* border-radius: 20rpx; */
  /* box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1); */
}

.gif-image {
  max-width: 100%;
  max-height: 60vh;
  border-radius: 10rpx;
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300rpx;
}

.loading-text {
  font-size: 32rpx;
  color: #999;
}

.expired-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300rpx;
}

.expired-emoji {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.expired-text {
  font-size: 32rpx;
  color: #666;
  text-align: center;
}

.save-container {
  padding: 20rpx 40rpx;
}

.save-btn {
  width: 90%;
  min-width: 550rpx;
  height: 88rpx;
  background-color: white;
  color: rgb(255, 173, 66);
  border: 2rpx solid rgb(255, 173, 66);
  border-radius: 10rpx;
  font-size: 32rpx;
  line-height: 60rpx;
  margin: 0 auto;
}

.save-btn:active {
  background-color: #06A050;
}

.save-btn[loading] {
  background-color: #ccc;
}

.ad-container {
  flex: 1;
  padding: 20rpx;
  background-color: white;
  margin: 0 20rpx 20rpx 20rpx;
  border-radius: 20rpx;
  min-height: 283rpx;
  max-height: 350rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  aspect-ratio: 515/283;
}
