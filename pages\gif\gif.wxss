/**gif.wxss**/
.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8f9fa;
}

.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background-color: white;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.nav-back {
  display: flex;
  align-items: center;
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  background-color: #f8f9fa;
}

.nav-back:active {
  background-color: #e9ecef;
}

.nav-back-icon {
  font-size: 32rpx;
  color: #666;
  margin-right: 10rpx;
}

.nav-back-text {
  font-size: 28rpx;
  color: #666;
}

.nav-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.nav-placeholder {
  width: 120rpx;
}

.gif-container {
  padding: 40rpx;
  background-color: white;
  margin: 20rpx 20rpx 10rpx 20rpx;
  text-align: center;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.gif-image {
  max-width: 100%;
  max-height: 70vh;
  border-radius: 15rpx;
  box-shadow: 0 8rpx 25rpx rgba(0,0,0,0.15);
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300rpx;
}

.loading-text {
  font-size: 32rpx;
  color: #999;
}

.expired-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300rpx;
}

.expired-emoji {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.expired-text {
  font-size: 32rpx;
  color: #666;
  text-align: center;
}

.save-container {
  padding: 20rpx 40rpx;
  background-color: white;
  margin: 10rpx 20rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.save-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 600;
  line-height: 88rpx;
  margin: 0;
  box-shadow: 0 6rpx 20rpx rgba(102, 126, 234, 0.4);
}

.save-btn:active {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.3);
}

.save-btn[loading] {
  background: #ccc;
  box-shadow: none;
}

.ad-container {
  flex: 1;
  padding: 20rpx;
  background-color: white;
  margin: 0 20rpx 20rpx 20rpx;
  border-radius: 20rpx;
  min-height: 283rpx;
  max-height: 350rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  aspect-ratio: 515/283;
}
