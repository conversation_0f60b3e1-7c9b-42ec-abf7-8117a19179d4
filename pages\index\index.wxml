<!--index.wxml-->
<view class="container">
  <scroll-view class="emoji-grid" scroll-y="true">
    <view class="emoji-list">
      <view
        class="emoji-item"
        wx:for="{{emojiList}}"
        wx:key="id"
        data-id="{{item.id}}"
        data-gif-url="{{item.gifUrl}}"
        bindtap="onEmojiTap"
      >
        <image class="emoji-preview" src="{{item.gifUrl}}" mode="aspectFill" lazy-load="true"></image>
        <view class="emoji-name">{{item.name}}</view>
      </view>
    </view>

    <!-- 隐藏的测试入口：在底部空白区域连续点击5次 -->
    <view class="hidden-trigger" bindtap="onTitleTap"></view>
  </scroll-view>
</view>
