<!--index.wxml-->
<view class="container">
  <scroll-view class="emoji-grid" scroll-y="true">
    <view class="emoji-list">
      <view
        class="emoji-item"
        wx:for="{{emojiList}}"
        wx:key="id"
        data-id="{{item.id}}"
        data-gif-url="{{item.gifUrl}}"
        bindtap="onEmojiTap"
      >
        <view class="emoji-icon">{{item.emoji}}</view>
        <view class="emoji-name">{{item.name}}</view>
      </view>
    </view>
  </scroll-view>

  <!-- 保留原有的隐藏入口功能 -->
  <view class="footer">
    <button class="wechat-btn" bindtap="openWechatAccount" bindlongpress="onTitleTap">打开公众号</button>
  </view>
</view>
